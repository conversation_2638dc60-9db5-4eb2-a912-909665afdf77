/*
 *************************************************************************
 * Vast Data OpenStorage Plugin for NetBackup
 * Copyright 2025 - Vast Data Inc., All Rights Reserved
 *************************************************************************
 */

#ifndef _VASTPLUGIN_H_
#define _VASTPLUGIN_H_

#include "../OST-SDK-11.1.1/src/include/stspi.h"
#include "../OST-SDK-11.1.1/src/include/stsi.h"
#include "macos_ost_v11_extensions.h"  // Include v11 extensions after OST SDK headers
#include <vector>
#include <string>
#include <memory>

// Forward declarations
class VastStorageServer;
class VastLSU;
class VastImage;
class VastRestClient;
class VastS3Client;

#ifdef __cplusplus
extern "C" {
#endif

/*********************************************************************************
 * Handle structures for Vast Data storage server, LSU list and Image
 *
 * These handles encapsulate the Vast Data specific implementations:
 * - Server handles contain VastStorageServer objects with REST API clients
 * - LSU handles manage Vast Data volumes/buckets via S3 and REST APIs
 * - Image handles represent backup images stored in Vast Data S3 buckets
 *********************************************************************************/

struct stsp_server_handle_s {
    VastStorageServer *vast_server;
    const sts_session_def_t *session;
};

struct stsp_lsu_list_handle_s {
    stsp_server_handle_t server_handle;
    std::vector<VastLSU *> *lsu_list;
    int cursor;
};

struct stsp_image_handle_s {
    VastImage *image_ptr;
    stsp_lsu_v7_t lsu;
    sts_image_def_v10_t image_def;
    const sts_session_def_t *session;
    
    // For copy operations
    bool need_copy_image;
    bool is_target_null;
    stsp_lsu_v7_t target_lsu;
    sts_image_def_v10_t target_image;
    sts_opname_v11_t imageset;
    int event_flag;
};

struct stsp_image_list_handle_s {
    stsp_server_handle_t server_handle;
    std::vector<VastImage *> *image_list;
    int cursor;
};

struct stsp_evc_handle_s {
    stsp_server_handle_t server_handle;
    int flags;
    int mode; // push mode = 1, pull mode = 0
    int pending;
    sts_evseqno_v11_t sequence;
    sts_evhandler_v11_t handler;
    sts_event_v11_t *event;
};

// Plugin entry point functions using correct OST SDK signatures

// Core API Functions
int stspi_init(sts_uint64_t masterVersion, const char *path, stspi_api_t *stspAPI);

int stspi_claim(const sts_server_name_v7_t serverName);

int stspi_get_server_prop_byname(
    const sts_session_def_v7_t *session,
    const sts_server_name_v7_t serverName,
    sts_server_info_v8_t *serverInfo);

int stspi_open_server(
    const sts_session_def_v7_t *session,
    const sts_server_name_v7_t sts_server_name,
    const sts_cred_v7_t *credentials,
    const sts_interface_v7_t stsInterface,
    stsp_server_handle_t *sh);

int stspi_get_server_prop(
    stsp_server_handle_t sh,
    sts_server_info_v8_t *serverInfo);

// LSU Management Functions
int stspi_get_lsu_prop_byname_v9(
    const stsp_lsu_v7_t *lsu, 
    sts_lsu_info_v9_t *lsuInfo);

int stspi_get_lsu_prop_byname_v11(
    const stsp_lsu_v7_t *lsu, 
    sts_lsu_info_v11_t *lsuInfo);

int stspi_open_lsu_list_v9(
    stsp_server_handle_t sh,
    const sts_lsu_def_v9_t *lsudef,
    stsp_lsu_list_handle_t *lsu_list_handle);

int stspi_open_lsu_list_v11(
    stsp_server_handle_t sh,
    const sts_lsu_def_v11_t *lsudef,
    stsp_lsu_list_handle_t *lsu_list_handle);

int stspi_list_lsu(
    stsp_lsu_list_handle_t lsuListHandle, 
    sts_lsu_name_v7_t *lsuName);

int stspi_label_lsu(
    const stsp_lsu_v7_t *lsu,
    sts_lsu_label_v9_t lsu_label);

// Image Management Functions
int stspi_get_image_prop_byname_v9(
    const stsp_lsu_v7_t *lsu, 
    const sts_image_def_v7_t *imageDefinition, 
    sts_image_info_v7_t *imageInfo);

int stspi_get_image_prop_byname_v10(
    const stsp_lsu_v7_t *lsu, 
    const sts_image_def_v10_t *imageDefinition, 
    sts_image_info_v10_t *imageInfo);

int stspi_delete_image_v9(
    const stsp_lsu_v7_t *lsu, 
    const sts_image_def_v7_t *imageDefinition, 
    int asyncFlag);

int stspi_delete_image_v10(
    const stsp_lsu_v7_t *lsu, 
    const sts_image_def_v10_t *imageDefinition, 
    int asyncFlag);

int stspi_create_image_v9(
    const stsp_lsu_v7_t *lsu, 
    const sts_image_def_v7_t *imageDefinition, 
    int pendingFlag, 
    stsp_image_handle_t *imageHandle);

int stspi_create_image_v10(
    const stsp_lsu_v7_t *lsu, 
    const sts_image_def_v10_t *imageDefinition, 
    int pendingFlag, 
    stsp_image_handle_t *imageHandle);

int stspi_open_image_v9(
    const stsp_lsu_v7_t *lsu, 
    const sts_image_def_v7_t *imageDefinition, 
    int mode, 
    stsp_image_handle_t *imageHandle);

int stspi_open_image_v10(
    const stsp_lsu_v7_t *lsu, 
    const sts_image_def_v10_t *imageDefinition, 
    int mode, 
    stsp_image_handle_t *imageHandle);

// Image I/O Functions
int stspi_read_image(
    stsp_image_handle_t ih, 
    void *buf, 
    sts_uint64_t length, 
    sts_uint64_t offset, 
    sts_uint64_t *bytesRead);

int stspi_write_image(
    stsp_image_handle_t ih,  
    sts_stat_v7_t *stat, 
    void *buf, 
    sts_uint64_t length, 
    sts_uint64_t offset, 
    sts_uint64_t *bytesWritten);

int stspi_write_image_meta(
    stsp_image_handle_t image_handle,
    void *buf, 
    sts_uint64_t len, 
    sts_uint64_t offset, 
    sts_uint64_t *byteswritten);

int stspi_read_image_meta(
    stsp_image_handle_t image_handle, 
    void *buf,
    sts_uint64_t len, 
    sts_uint64_t offset, 
    sts_uint64_t *bytesread);

// Cleanup Functions
int stspi_close_image(
    stsp_image_handle_t ih, 
    int completeFlag, 
    int forceFlag);

int stspi_close_image_list(
    const stsp_image_list_handle_t image_list_handle);

int stspi_close_lsu_list(
    const stsp_lsu_list_handle_t lsuListHandle);

int stspi_close_server(
    stsp_server_handle_t sh);

int stspi_terminate();

// Advanced Features (v11)
int stspi_copy_image_v11(
    const stsp_lsu_v7_t *to_lsu, 
    const sts_image_def_v10_t *to_img,
    const stsp_lsu_v7_t *from_lsu, 
    const sts_image_def_v10_t *from_img, 
    const sts_opname_v11_t imageset,
    int eventflag);

int stspi_async_copy_image_v11(
    const stsp_lsu_v7_t *to_lsu, 
    const sts_image_def_v10_t *to_img,
    const stsp_lsu_v7_t *from_lsu, 
    const sts_image_def_v10_t *from_img, 
    stsp_opid_t *opid,
    const sts_opname_v11_t imageset,
    int eventflag);

// Event Management
int stspi_open_evchannel_v11(
    const sts_session_def_v7_t *sd,
    const sts_server_name_v7_t server, 
    const sts_cred_v7_t *cred,
    const sts_interface_v7_t iface, 
    sts_evhandler_v11_t handler,
    sts_event_v11_t *event, 
    int flags, 
    sts_evseqno_v11_t evseqno, 
    stsp_evc_handle_t *pevc_handle);

int stspi_get_event_v11(
    stsp_evc_handle_t evc_handle,
    sts_event_v11_t *event);

int stspi_close_evchannel_v9(
    stsp_evc_handle_t evc_handle);

// Additional missing function declarations
int stspi_open_image_list_v10(
    const stsp_lsu_v7_t* lsu,
    const sts_image_list_def_v10_t* imageListDef,
    stsp_image_list_handle_t* image_list_handle);

int stspi_list_image_v10(
    stsp_image_list_handle_t image_list_handle,
    sts_image_info_v10_t* imageInfo);

int stspi_query_operation_v11(
    stsp_server_handle_t sh,
    stsp_opid_t opid,
    sts_operation_info_v11_t* op_info);

int stspi_cancel_operation_v11(
    stsp_server_handle_t sh,
    stsp_opid_t opid);

int stspi_get_plugin_info_v11(
    sts_plugin_info_v11_t* plugin_info);

int stspi_health_check_v11(
    stsp_server_handle_t sh,
    sts_health_info_v11_t* health_info);

// Additional utility functions for configuration and diagnostics
int stspi_get_config_v11(
    stsp_server_handle_t sh,
    const char* config_key,
    char* config_value,
    size_t value_size);

int stspi_set_config_v11(
    stsp_server_handle_t sh,
    const char* config_key,
    const char* config_value);

int stspi_get_statistics_v11(
    stsp_server_handle_t sh,
    sts_statistics_v11_t* stats);

// OST API Constants and Error Codes (if not defined in stspi.h)
#ifndef STS_SUCCESS
#define STS_SUCCESS                    0
#define STS_ERR_INVALID_PARAMETER     -1
#define STS_ERR_NOT_IMPLEMENTED       -2
#define STS_ERR_INTERNAL              -3
#define STS_ERR_VERSION_MISMATCH      -4
#define STS_ERR_NOT_SUPPORTED         -5
#define STS_ERR_INVALID_SERVER_NAME   -6
#define STS_ERR_NO_MORE_DATA          -7
#define STS_ERR_CONNECTION_FAILED     -8
#define STS_ERR_AUTHENTICATION_FAILED -9
#define STS_ERR_TIMEOUT               -10
#define STS_ERR_FILE_NOT_FOUND        -11
#define STS_ERR_INSUFFICIENT_SPACE    -12
#define STS_ERR_OPERATION_CANCELLED   -13
#endif

#ifndef STS_MINIMUM_VERSION
#define STS_MINIMUM_VERSION           0x0B010000  // Version 11.1.0.0
#endif

// Capability flags
#ifndef STS_CAP_ASYNC_IO
#define STS_CAP_ASYNC_IO              0x00000001
#define STS_CAP_COPY_IMAGE            0x00000002
#define STS_CAP_EVENTS                0x00000004
#define STS_CAP_METADATA              0x00000008
#define STS_CAP_DEDUPLICATION         0x00000010
#define STS_CAP_ENCRYPTION            0x00000020
#define STS_CAP_COMPRESSION           0x00000040
#endif

// Feature flags
#ifndef STS_FEATURE_DEDUP
#define STS_FEATURE_DEDUP             0x00000001
#define STS_FEATURE_ENCRYPTION        0x00000002
#define STS_FEATURE_COMPRESSION       0x00000004
#define STS_FEATURE_REPLICATION       0x00000008
#endif

// Event channel flags
#ifndef STS_EVCFLAG_PUSH
#define STS_EVCFLAG_PUSH              0x00000001
#define STS_EVCFLAG_PULL              0x00000002
#endif

// Health status codes
#ifndef STS_HEALTH_OK
#define STS_HEALTH_OK                 0
#define STS_HEALTH_WARNING            1
#define STS_HEALTH_ERROR              2
#define STS_HEALTH_CRITICAL           3
#endif

// Operation modes
#ifndef STS_MODE_READ
#define STS_MODE_READ                 1
#define STS_MODE_WRITE                2
#define STS_MODE_APPEND               3
#endif

// API Export macro
#ifndef API_Export
#ifdef _WIN32
#define API_Export __declspec(dllexport)
#else
#define API_Export __attribute__((visibility("default")))
#endif
#endif

#ifdef __cplusplus
}
#endif

#endif /* _VASTPLUGIN_H_ */