/*
 * macOS compatibility header for OST SDK
 * This replaces the Linux platform headers for macOS builds
 */

#ifndef _MACOS_STSPLAT_H_
#define _MACOS_STSPLAT_H_

#include <stdint.h>
#include <sys/types.h>

// macOS equivalents for Linux types
typedef uint8_t  __u8;
typedef uint16_t __u16;
typedef uint32_t __u32;
typedef uint64_t __u64;

typedef int8_t   __s8;
typedef int16_t  __s16;
typedef int32_t  __s32;
typedef int64_t  __s64;

// Platform-specific definitions for macOS
#define STS_PLATFORM_MACOS 1

// Thread and synchronization primitives
#include <pthread.h>
typedef pthread_mutex_t STS_MUTEX;
typedef pthread_cond_t  STS_COND;
typedef pthread_t       STS_THREAD;

// File system types
typedef off_t STS_OFFSET;
typedef size_t STS_SIZE;

// Network types
typedef int STS_SOCKET;
#define STS_INVALID_SOCKET (-1)

// Memory alignment
#define STS_ALIGN(x) __attribute__((aligned(x)))

// Compiler attributes
#define STS_INLINE inline
#define STS_FORCE_INLINE __attribute__((always_inline)) inline

// Export/Import macros for shared libraries
#define STS_EXPORT __attribute__((visibility("default")))
#define STS_IMPORT

// Platform-specific constants
#define STS_MAX_PATH 1024
#define STS_PATH_SEPARATOR '/'

// Error codes (compatible with Linux errno values) - only define if not already defined by OST SDK
#ifndef STS_EPERM
#define STS_EPERM     1
#define STS_ENOENT    2
#define STS_ESRCH     3
#define STS_EINTR     4
#define STS_EIO       5
#define STS_ENXIO     6
#define STS_E2BIG     7
#define STS_ENOEXEC   8
#define STS_EBADF     9
#define STS_ECHILD    10
#define STS_EAGAIN    11
#define STS_ENOMEM    12
#define STS_EACCES    13
#define STS_EFAULT    14
#define STS_ENOTBLK   15
#define STS_EBUSY     16
#define STS_EEXIST    17
#define STS_EXDEV     18
#define STS_ENODEV    19
#define STS_ENOTDIR   20
#define STS_EISDIR    21
#define STS_EINVAL    22
#define STS_ENFILE    23
#define STS_EMFILE    24
#define STS_ENOTTY    25
#define STS_ETXTBSY   26
#define STS_EFBIG     27
#define STS_ENOSPC    28
#define STS_ESPIPE    29
#define STS_EROFS     30
#define STS_EMLINK    31
#define STS_EPIPE     32
#define STS_EDOM      33
#define STS_ERANGE    34
#endif

// OST SDK specific error codes - only define if not already defined by OST SDK
#ifndef STS_ERR_INTERNAL
#define STS_ERR_INTERNAL                1001
#define STS_ERR_INVALID_PARAMETER       1002
#define STS_ERR_VERSION_MISMATCH        1003
#define STS_ERR_NOT_SUPPORTED           1004
#define STS_ERR_INVALID_SERVER_NAME     1005
#define STS_ERR_NO_MORE_DATA            1006
#define STS_ERR_BUFFER_TOO_SMALL        1007
#define STS_ERR_CONNECT                 1008
#endif

// OST SDK mode constants
#ifndef STS_MODE_READ
#define STS_MODE_READ                   1
#define STS_MODE_WRITE                  2
#endif

// OST SDK image status constants
#ifndef STS_IMG_STATUS_COMPLETE
#define STS_IMG_STATUS_COMPLETE         1
#define STS_IMG_TYPE_BACKUP             1
#endif

#endif /* _MACOS_STSPLAT_H_ */
